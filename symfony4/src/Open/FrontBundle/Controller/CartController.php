<?php

namespace Open\FrontBundle\Controller;

use AppBundle\Entity\Address;
use AppBundle\Entity\Cart;
use AppBundle\Entity\CartHistoric;
use AppBundle\Entity\CartShippingOption;
use AppBundle\Entity\Company;
use AppBundle\Entity\ShippingPoint;
use AppBundle\Entity\Site;
use AppBundle\Entity\User;
use AppBundle\Exception\CartException;
use AppBundle\Factory\CartFactory;
use AppBundle\Model\Cart\CartMerchant;
use AppBundle\Model\Cart\CartNotification;
use AppBundle\Model\DetailedOffer;
use AppBundle\Services\AddressService;
use AppBundle\Services\CartNotificationService;
use AppBundle\Services\CartService;
use AppBundle\Services\CompanyCatalogService;
use AppBundle\Services\JobService;
use AppBundle\Services\MerchantOrderService;
use AppBundle\Services\OfferService;
use AppBundle\Services\OrderService;
use AppBundle\Services\PaymentService;
use AppBundle\Services\SecurityService;
use AppBundle\Services\ShippingPointService;
use AppBundle\Services\ShippingService;
use AppBundle\Services\SiteService;
use AppBundle\Services\SpecificPriceService;
use AppBundle\Services\UserBddService;
use AppBundle\Services\WishListService;
use AppBundle\Services\WPSService;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Exception;
use Open\FrontBundle\Helpers\CartItemHelper;
use Open\IzbergBundle\Api\ApiException;
use Open\LogBundle\Utils\EventNameEnum;
use Open\LogBundle\Utils\LogUtil;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Contracts\Translation\TranslatorInterface;

class CartController extends PaymentController
{
    private const QUERY_PARAM_QTY = 'qty';
    private const QUERY_PRODUCT_ID = "productId";
    private const QUERY_PARAM_ITEM_ID = 'itemId';

    private const QUERY_PARAM_USER_ID = 'userId';
    private const QUERY_PARAM_CART_ID = 'cartId';
    private const QUERY_PARAM_COMMENT = 'comment';
    private const QUERY_PARAM_SITE_ID = 'siteId';
    private const QUERY_PARAM_CURRENCY = 'currency';
    private const QUERY_PARAM_ADDRESS_ID = 'addressId';
    private const QUERY_PARAM_SHIPPING_POINT_ID = 'shippingPointId';
    private const QUERY_PARAM_DOCUMENTS_REQUESTS = 'documentsRequests';

    /**
     * @param Request $request
     * @param ShippingPointService $shippingPointService
     * @param CartService $cartService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/documents/save', name: 'cart.documents.save', methods: ['POST'])]
    public function saveCartDocuments(
        Request $request,
        ShippingPointService $shippingPointService,
        CartService $cartService
    ): Response
    {
        $cartId = intval($request->get('cartId'));
        $shippingPointId = intval($request->get('shippingPointId'));
        $documents = $request->get('documents', []);

        if ($cartId && $shippingPointService->isValidShippingPointDocumentsRequests($shippingPointId, $documents)) {
            $cart = $cartService->getCartDb($cartId);
            $cart->setDocumentsRequest($documents);
            $cartService->saveDatabaseCart($cart);
            return new Response('', Response::HTTP_CREATED);
        }

        return new Response('', Response::HTTP_BAD_REQUEST);
    }

    /**
     * @param Request $request
     * @param $currencyOrCartId
     * @param WishListService $wishListService
     * @param CartService $cartService
     * @param ShippingService $shippingService
     * @param CartNotificationService $cartNotificationService
     * @param SiteService $siteService
     * @param SecurityService $securityService
     * @param PaymentService $paymentService
     * @param WPSService $wpsService
     * @param OrderService $orderService
     * @param MerchantOrderService $merchantOrderService
     * @param TranslatorInterface $translator
     * @param JobService $jobService
     * @return Response
     * @throws ORMException
     * @throws OptimisticLockException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/detail/{currencyOrCartId}', name: 'cart.details.before_buy')]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/detail/{currencyOrCartId}/shipping', name: 'cart.details.shipping')]
    public function cartDetailsAction(
        Request $request,
        $currencyOrCartId,
        WishListService $wishListService,
        CartService $cartService,
        ShippingService $shippingService,
        CartNotificationService $cartNotificationService,
        SiteService $siteService,
        SecurityService $securityService,
        PaymentService $paymentService,
        WPSService $wpsService,
        OrderService $orderService,
        MerchantOrderService $merchantOrderService,
        TranslatorInterface $translator,
        JobService $jobService
    ): Response {
        $routeName = $request->attributes->get('_route');

        $requestID = uniqid();

        $this->logger->info(
            'Start cart validation',
            LogUtil::buildContext([
                'requestID' => $requestID,
            ])
        );

        /**
         * @var User $user
         */
        $user = $this->getUser();
        $isCartCreator = true;
        $sites = [];
        $cart = null;

        $this->logger->info(
            'Cart validation point 1',
            LogUtil::buildContext([
                'requestID' => $requestID,
                'userID' => $user->getId(),
            ])
        );

        //current cart is used to distinguish when we consult the current cart of the user, or an other cart (pending carts)
        if (is_numeric($currencyOrCartId)) {
            $cart = $cartService->findCart($user, $currencyOrCartId);
            $currentCart = false;
        } else {
            $currentCart = true;
            $cart = $cartService->findOrCreateUserCart($user, $currencyOrCartId);
        }

        if (!$cart) {
            throw new \Exception('Cannot find or create user cart');
        }

        $isUserAuthorizeToBuy = $cartService->isUserAuthorizedToBuy($user);
        $isCartCreator = $cart->isCartCreator($user);

        $this->logger->info(
            'Cart validation point 2',
            LogUtil::buildContext([
                'requestID' => $requestID,
                'userID' => $user->getId(),
                'isUserAuthorizeToBuy' => $isUserAuthorizeToBuy,
                'isCartCreator' => $isCartCreator,
                'currentCart' => $currentCart,
            ])
        );

        // Find users for assign cart if not exists
        if (!is_null($user->getCompany())) {

            $this->logger->info(
                'Cart validation point 3',
                LogUtil::buildContext([
                    'requestID' => $requestID,
                    'companyID' => $user->getCompany()->getId()
                ])
            );

            //if user is buyer admin, he can all sites
            if ($securityService->isAdminCompany($user)) {
                $sites = $siteService->getSitesWithAtLeastOneAddressByCompany($user->getCompany()->getId());
            } else {
                $sites = $siteService->getUserSitesWithAtLeastOneAddress($user);
            }
        }

        //we sync the quantity, only for current carts
        //for example, for pending carts, we don't want to sync quantity
        if ($currentCart) {
            $cartService->syncTotalItemFromCart($cart, $user);

            $this->logger->info(
                'Cart validation point 4',
                LogUtil::buildContext([
                    'requestID' => $requestID,
                ])
            );
        }

        $template = '@OpenFront/cart/cart_details.html.twig';

        $shipping = $shippingService->buildCartMerchantShippingOptionsV2($cart, $this->getUser());

        if ($routeName === 'cart.details.shipping') {

            $this->logger->info(
                'Cart validation point 5. Shipping.',
                LogUtil::buildContext([
                    'requestID' => $requestID,
                ])
            );

            if (!$cart->isValid()) {
                return $this->redirectToRoute('cart.details.before_buy', ['currencyOrCartId' => $currencyOrCartId]);
            }

            $this->logger->info(
                'Cart validation point 6. Cart is valid.',
                LogUtil::buildContext([
                    'requestID' => $requestID,
                ])
            );

            $shipping = $shippingService->buildCartMerchantShippingOptions($cart, $this->getUser());

            $this->logger->info(
                'Cart validation point 7',
                LogUtil::buildContext([
                    'requestID' => $requestID,
                    'shipping' => !empty($shipping)
                ])
            );

            $template = '@OpenFront/cart/cart_shipping_details.html.twig';

            if ($shipping) {

                $this->logger->info(
                    'Cart validation point 8. Prepare to merge cart merchant shipping VatRate with cart merchant subtotalvat.',
                    LogUtil::buildContext([
                        'requestID' => $requestID,
                    ])
                );

                // merge cart merchant shipping VatRate with cart merchant subtotalvat
                $cartMerchants = array_map(
                    function (CartMerchant $cartMerchant) use ($shipping, $cart) {
                        $shippingMerchant = $shipping->findCartMerchantShippingOptions($cartMerchant->getId());

                        if (!$shippingMerchant) {
                            return $cartMerchant;
                        }

                        $shippingVatRate = $shippingMerchant->getVatRate();
                        $merchantSubTotalVat = $cartMerchant->getSubTotalVat();
                        $cartSubTotalVat = $cart->getSubTotalVat();

                        if (!array_key_exists($shippingVatRate, $merchantSubTotalVat)) {
                            $merchantSubTotalVat[$shippingVatRate] = 0;
                            krsort($merchantSubTotalVat);
                        }

                        if (!array_key_exists($shippingVatRate, $cartSubTotalVat)) {
                            $cartSubTotalVat[$shippingVatRate] = 0;
                            krsort($cartSubTotalVat);
                        }

                        $cartMerchant->setSubTotalVat($merchantSubTotalVat);
                        $cart->setSubTotalVat($cartSubTotalVat);

                        return $cartMerchant;
                    },
                    $cart->getMerchants()
                );
                $cart->setMerchants($cartMerchants);

                $this->logger->info(
                    'Cart validation point 9. Cart merchants set.',
                    LogUtil::buildContext([
                        'requestID' => $requestID,
                    ])
                );
            }
        }

        $isUserValidAssign = $cartService->isUserValidAssign($user, $cart->getId());

        $isUserAuthorizeReject = $cartService->isUserAuthorizeReject($user, $cart->getId());

        $cartHistoric = $cartService->getCartHistoric($cart->getId());

        $this->logger->info(
            'Cart validation point 10',
            LogUtil::buildContext([
                'requestID' => $requestID,
                'isUserValidAssign' => $isUserValidAssign,
                'isUserAuthorizeReject' => $isUserAuthorizeReject,
            ])
        );

        //in addition, we need to add stuff relative to payment selection
        $paymentModesFomElement = $this->initPaymentModesForm($request, $cart, $paymentService);

        $this->logger->info(
            'Cart validation point 11',
            LogUtil::buildContext([
                'requestID' => $requestID,
            ])
        );

        /** @var Form $form */
        $form = $paymentModesFomElement['form']->handleRequest($request);

        $this->logger->info(
            'Cart validation point 12',
            LogUtil::buildContext([
                'requestID' => $requestID,
            ])
        );

        if ($form->isSubmitted() && $form->isValid() && $cart->isValid()) {

            $this->logger->info(
                'Cart validation point 13',
                LogUtil::buildContext([
                    'requestID' => $requestID,
                ])
            );

            $cartNotificationService->checkCartNotification($cart);
            if (!$cart->hasNotifications()) {

                $this->logger->info(
                    'Cart validation point 14',
                    LogUtil::buildContext([
                        'requestID' => $requestID,
                    ])
                );

                if ($shipping) {

                    $this->logger->info(
                        'Cart validation point 15',
                        LogUtil::buildContext([
                            'requestID' => $requestID,
                        ])
                    );

                    $selectedShippings = [];
                    foreach ($form->getData()['merchantShippings'] as $merchant => $shippingOption) {
                        $merchant = (int)str_replace('merchant-shipping-', '', $merchant);
                        $selectedShippings[$merchant] = $shippingOption;
                    }

                    $shippingService->saveSelectedMerchantShippingOptionsV2($shipping);

                    $this->logger->info(
                        'Cart validation point 16',
                        LogUtil::buildContext([
                            'requestID' => $requestID,
                        ])
                    );
                }

                /** @var Address $newBillingAddress */
                $newBillingAddress = $form->getData()['billingAddress'];
                $billingAddress = $this->checkIfNewBillingAddress($newBillingAddress);

                $this->logger->info(
                    'Cart validation point 17',
                    LogUtil::buildContext([
                        'requestID' => $requestID,
                    ])
                );

                $cartService->checkoutCart(
                    cart: $cart,
                    user: $user,
                    shippingAddressId: $form->getData()['addressId'] ?? $cart->getShippingPoint()->getId(),
                    paymentMode: $form->getData()['term'],
                    locale: $request->getLocale(),
                    billingAddress: $billingAddress
                );

                $this->logger->info(
                    'Cart validation point 18',
                    LogUtil::buildContext([
                        'requestID' => $requestID,
                    ])
                );

                return $this->handlePaymentRequest(
                    cart: $cart,
                    cartService: $cartService,
                    wpsService: $wpsService,
                    orderService: $orderService,
                    merchantOrderService: $merchantOrderService,
                    translator: $translator,
                    jobService: $jobService,
                    validationNumber: $form->getData()['validationNumber'],
                    accountingEmail: $form->getData()['accountingEmail']
                );
            }

            $this->logger->info(
                'Cart validation point 19',
                LogUtil::buildContext([
                    'requestID' => $requestID,
                ])
            );

            /** @var CartNotification $cartNotification */
            foreach ($cart->getNotifications() as $cartNotification) {
                $cartNotificationService->applyCartNotification($cartNotification);
                $this->addFlash(
                    'info',
                    $translator->trans($cartNotification->getMessage(), [], self::TRANSLATION_DOMAIN)
                );
            }

            $this->logger->info(
                'Cart validation point 20',
                LogUtil::buildContext([
                    'requestID' => $requestID,
                ])
            );

            return $this->redirectToRoute('cart.details.before_buy', ['currencyOrCartId' => $currencyOrCartId]);
        }

        //new variable to known is user is authorized to edit cart
        $isUserAuthorizeToEditCart = $isUserValidAssign || (empty($cartService->getCartAssignmentHistoric($cart->getId())) && $isCartCreator);

        foreach ($cart->getMerchants() as $merchant) {
            CartItemHelper::setQuantityDemandOrOnStock($merchant, "cart");

            foreach ($merchant->getItems() as $merchantItem) {
                $cartService->splitDeliveryCart($merchantItem, $request->getLocale(), $cart->getId(), $this->getUser());
            }

            $merchant->cgv = $this->generateUrl(
                'front.merchant.general_sales_condition',
                ['merchantId' => $merchant->getId()]
            );
        }

        // All without api and administrator can split delivery date
        $cart->canSplitDeliveryDate = $user->canSplitDeliveryDate();

        return $this->render(
            $template,
            array_merge(
                $paymentModesFomElement['viewParams'],
                [
                    'user' => $this->getUser(),
                    'cart' => $cart,
                    'currencyOrCartId' => $currencyOrCartId,
                    'isUserAuthorizeToBuy' => $isUserAuthorizeToBuy,  //whether the user has BUYER_PAYER or BUYER_ADMIN role
                    'isUserValidAssign' => $isUserValidAssign,        //whether the current user is the assigned cart user
                    'isUserAuthorizeToEditCart' => $isUserAuthorizeToEditCart, //whether the current user is authorized to edit this cart
                    'isUserAuthorizeToAutoAssign' => !$isUserAuthorizeToEditCart && $cartService->isUserInAssignmentChains($this->getUser(), $cart->getId()),
                    'isUserAuthorizeReject' => $isUserAuthorizeReject,
                    'sites' => $sites,
                    'isCartCreator' => $isCartCreator,
                    'historic' => array_filter(
                        $cartService->getCartHistoric($cart->getId()),
                        function (CartHistoric $cartHistoric) {
                            return (
                                $cartHistoric->getStatus() === Cart::STATUS_ASSIGN
                                || $cartHistoric->getStatus() === Cart::STATUS_CREATE
                                || $cartHistoric->getStatus() === Cart::STATUS_REJECTED
                            );
                        }
                    ),
                    'companyValid' => $this->validateCompany(),
                    'wishLists' => $wishListService->getUserWishList($user->getId(), $cart->getCurrency()),
                    'shipping' => $shipping
                ]
            )
        );
    }

    /**
     * @param $cartId
     * @param CartService $cartService
     * @param TranslatorInterface $translator
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/assign/my/{cartId}', name: 'cart.auto.assign', methods: ['GET'])]
    public function assignToMyselfCartAction(
        $cartId,
        CartService $cartService,
        TranslatorInterface $translator
    )
    {
        //security check
        if (!$cartService->isUserInAssignmentChains($this->getUser(), $cartId)) {
            throw $this->createAccessDeniedException();
        }

        try {
            //get cart from db
            /** @var Cart $cartDB */
            $cartDB = $cartService->getCartDb($cartId);
            /** @var User $user */
            $user = $this->getUser();

            if ($cartDB !== null && $user !== null) {
                $cartService->assignCart(
                    $cartId,
                    $cartDB->getCreatedUser()->getId(),
                    $user->getId(),
                    $cartDB->getSite()->getId(),
                    $cartDB->getAddress()->getId(),
                    "",
                    $cartDB->getValidationNumber(),
                    $cartDB->getAccountingEmail(),
                    $cartDB->getBillingAddress()
                );
            }
        } catch (Exception $e) {
            $this->addFlash("error", $translator->trans('cart.assign.error', array(), 'AppBundle'));
        }

        return $this->redirectToRoute("cart.pending.details", ["cartId" => $cartId]);
    }

    /**
     * @param $cartId
     * @param $currency
     * @param CartService $cartService
     * @return RedirectResponse
     * @throws ORMException
     * @throws OptimisticLockException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/clear/{cartId}/{currency}', name: 'cart.clear', methods: ['GET'])]
    public function cartClearAction(
        $cartId,
        $currency,
        CartService $cartService
    )
    {
        $user = $this->getUser();
        $this->removeShippingOptions($cartId);
        $cartService->clearUserCurrentCarts($user, $cartId);

        return $this->redirectToRoute("cart.details.before_buy", ["currencyOrCartId" => $currency]);
    }

    /**
     * check if the company of the authenticated user is valid
     */
    private function validateCompany()
    {
        $company = $this->getCompany();

        $stepValid = ($company->getStep() >= 2);

        $hasShippingPoints = call_user_func(
            function (Company $company) {

                $sites = $company->getSites();
                if (!count($sites)) {
                    return false;
                }

                /** @var Site $firstSite */
                $firstSite = $sites->first();
                $shippingPoints = $firstSite->getShippingPoints();
                if (!count($shippingPoints)) {
                    return false;
                }

                /** @var ShippingPoint $firstShippingPoints */
                $firstShippingPoints = $shippingPoints->first();
                if (!$firstShippingPoints->getAddress()) {
                    return false;
                }
                return (bool) $firstShippingPoints->getAddress()->getAddress();
            },
            $company
        );

        return ($stepValid && $hasShippingPoints);
    }


    /**
     * @param Request $request
     * @param UserBddService $userService
     * @param SiteService $siteService
     * @param SecurityService $securityService
     * @param TranslatorInterface $translator
     * @return JsonResponse
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/buyers/', name: 'cart.details.buyers.list')]
    public function getBuyersForCostCenterAction(
        Request $request,
        UserBddService $userService,
        SiteService $siteService,
        SecurityService $securityService,
        TranslatorInterface $translator
    )
    {
        $shippingPointId = $request->get('shippingPointId');
        $siteId = $request->get('siteId');
        $addressId = $request->get('addressId');

        if (!$shippingPointId) {
            if ($siteId == null || $addressId == null) {
                return new JsonResponse($translator->trans('cart.cost_center.noSiteId', array(), 'AppBundle'), 500);
            }

            //security check: First, check that the current user is authorized to access this site
            $site = $siteService->get($siteId);
        } else {
            $site = $siteService->getFromShippingPointId($shippingPointId);
        }

        if ($site && $this->getUser()->getCompany()->getId() !== $site->getCompany()->getId()) {
            $this->logger->error(
                "user try to access a site that he doesn't own. Move to state ACCESS_DENIED",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::SECURITY_EVENT,
                    LogUtil::USER_NAME => $this->getUsername(),
                    'requestedSiteId' => $siteId,
                ])
            );
            throw $this->createAccessDeniedException($translator->trans(self::ACTION_DENIED_EXCEPTION));
        }

        if ($request->isXmlHttpRequest() && $site) {
            // Get company admins
            $companyAdmins = $securityService->getCompanyAdminUsers($this->getCompany());

            // All buyers except Myself
            $buyersList = $userService->getUsersBySite($site->getId(), $this->getUser()->getId());

            /** @var User $companyAdmin */
            foreach ($companyAdmins as $companyAdmin) {
                if ($this->getUser()->getId() != $companyAdmin->getId()) {
                    $buyersList[] = $companyAdmin;
                }
            }

            return new JsonResponse(json_encode(array_unique($buyersList)));
        }

        return new JsonResponse($translator->trans('cart.cost_center.error', array(), 'AppBundle'), 500);
    }

    /**
     * @param Request $request
     * @param int $cartId
     * @param CartService $cartService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/history/{cartId}', name: 'cart.details.historic')]
    public function getCartHistoryAction(
        Request $request,
        int $cartId,
        CartService $cartService
    )
    {
        if ($request->isXmlHttpRequest()) {
            $cartHistoric = $cartService->getCartHistoric($cartId);
            return new JsonResponse(json_encode($cartHistoric));
        }

        return new Response('KO', 500);
    }

    /**
     * @param Request $request
     * @param CartService $cartService
     * @param OfferService $offerService
     * @param ShippingService $shippingService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/item/remove', name: 'cart.details.remove_item', methods: ['DELETE'])]
    public function removeItemFromCartAction(
        Request $request,
        CartService $cartService,
        OfferService $offerService,
        ShippingService $shippingService
    )
    {

        try {
            if ($request->isXmlHttpRequest() && $request->request->has(self::QUERY_PARAM_ITEM_ID)
                && $request->request->has(self::QUERY_PARAM_CART_ID)) {
                $cartItemId = $request->request->get(self::QUERY_PARAM_ITEM_ID);
                $offerId = $request->get('offerId');
                $cartId = $request->get('cartId');
                $offer = $offerService->findDetailedOfferById($offerId, $this->getUser()->getCompany());
                $this->removeShippingOptions($cartId, $offer);

                $cartService->removeItem($cartItemId);
                $addressId = $cartService->findCartAddressId($cartId);

                $shippingService->deleteCache(
                    $this->getUser()->getId(),
                    $cartId,
                    $offer->getOffer()->getMerchant()->getId(),
                    $addressId
                );
            }
        } catch (Exception $e) {
            //in that case we don't want to let the user with a cart that he cannot clear
            $this->logger->error(
                "an error occured while trying to remove item from carts: ",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::CART,
                    LogUtil::USER_NAME => $this->getUsername(),
                ])
            );
        }

        //we always return a 200. If an error occurred, the cart will be automatically replaced by a new one
        return new JsonResponse('', 200);
    }

    /**
     * @param Request $request
     * @param CartService $cartService
     * @param OfferService $offerService
     * @param ShippingService $shippingService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/item/refresh', name: 'cart.details.refresh_item', methods: ['PUT'])]
    public function refreshItemFromCartAction(
        Request $request,
        CartService $cartService,
        OfferService $offerService,
        ShippingService $shippingService
    )
    {
        try {
            if ($request->isXmlHttpRequest() && $request->request->has(self::QUERY_PARAM_ITEM_ID)
                && $request->request->has(self::QUERY_PARAM_CART_ID)) {
                $offerId = $request->get('offerId');
                $cartId = $request->get('cartId');
                $quantity = $request->get('quantity');
                $offer = $offerService->findDetailedOfferById($offerId, $this->getUser()->getCompany());
                $offerMoq = $offer->getOffer()->getMoq();
                $offerBatchSize = $offer->getOffer()->getBatchSize();


                $updateQuantity = function($quantity, $batchSize, $moq) {

                    $quantity = ($quantity < $moq) ? $moq : $quantity;

                    if ($batchSize > $moq) {
                        $quantity = $batchSize;
                    }

                    if (($quantity % $batchSize) > 0) {
                        $quantity +=  $quantity % $batchSize;
                    }

                    return $quantity;
                };

                $quantity = $updateQuantity($quantity, $offerBatchSize, $offerMoq);
                $this->removeShippingOptions($cartId, $offer);

                $cartService->addOfferToCart(
                    $offer->getOffer(),
                    $this->getUser(),
                    intval($quantity),
                    $cartId,
                    true
                );

                $addressId = $cartService->findCartAddressId($cartId);

                $shippingService->deleteCache(
                    $this->getUser()->getId(),
                    $cartId,
                    $offer->getOffer()->getMerchant()->getId(),
                    $addressId
                );
            }
        } catch (Exception $e) {
            //in that case we don't want to let the user with a cart that he cannot clear
            $this->logger->error(
                "an error occured while trying to refresh an item from carts: ",
                LogUtil::buildContext([
                    LogUtil::EVENT_NAME => EventNameEnum::CART,
                    LogUtil::USER_NAME => $this->getUsername(),
                ])
            );
        }

        //we always return a 200. If an error occurred, the cart will be automatically replaced by a new one
        return new JsonResponse('', 200);
    }


    /**
     * add a product to a cart
     * @param Request $request
     * @param CompanyCatalogService $companyCatalogService
     * @param SpecificPriceService $specificPriceService
     * @param CartService $cartService
     * @param OfferService $offerService
     * @param ShippingService $shippingService
     * @return Response
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/item/add', name: 'front.cart.add', methods: ['POST'])]
    public function addOfferToCartAction(
        Request $request,
        CompanyCatalogService $companyCatalogService,
        SpecificPriceService $specificPriceService,
        CartService $cartService,
        OfferService $offerService,
        ShippingService $shippingService
    )
    {
        $productId = $request->request->get(self::QUERY_PRODUCT_ID);
        $quantity = $request->request->get(self::QUERY_PARAM_QTY);

        $offer = $offerService->findDetailedOfferById($productId, $this->getUser()->getCompany());

        if ($offer->getOffer()->getBatchSize() != null && $quantity % $offer->getOffer()->getBatchSize() > 0) {
            return new Response('', 500);
        }

        $buyerCatalogRef = $companyCatalogService->findBuyerReference($this->getUser(), $offerService->findCatalogReferences($productId));

        $cartId = $cartService->addOfferToCart(
            $offer->getOffer(),
            $this->getUser(),
            intval($quantity),
            null,
            false,
            [
                'Buyer-internal-ref' => $buyerCatalogRef,
                'frame_contract' => $offer->getOffer()->getFrameContract(),
            ]
        );


        $shippingService->deleteCache(
            $this->getUser()->getId(),
            $cartId,
            $offer->getOffer()->getMerchant()->getId()
        );

        // Récupération du nouveau compteur pour la currency
        $compteur = [];
        $compteur['EUR'] = $this->getUser()->getItemInCartEUR();
        $compteur['USD'] = $this->getUser()->getItemInCartUSD();

        return new JsonResponse($compteur, 200);
    }

    /**
     * @param Request $request
     * @param string $cartItemId
     * @param CartService $cartService
     * @param OfferService $offerService
     * @return Response
     * @throws CartException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/item/{cartItemId}', name: 'cart.details.update_item_extra_info', methods: ['POST'])]
    public function updateCartItemExtraInfoAction(
        Request $request,
        string $cartItemId,
        CartService $cartService,
        OfferService $offerService
    ): Response {
        $form = $this->createFormBuilder(null, ['csrf_protection' => true])
            ->add('field', TextType::class, ['required' => true, 'constraints' => new NotBlank()])
            ->add('value', TextType::class)
            ->add('split_delivery', TextType::class)
            ->add('offer_id', TextType::class)
            ->add('total_qty', TextType::class)
            ->add('cart_id', TextType::class)
            ->getForm();

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $field = $form->getData()['field'];
            $value = $form->getData()['value'];
            $cartService->updateCartItemExtraInfo(intval($cartItemId), $field, $value);

            if (!empty($form->getData()['split_delivery'])) {

                // update quantity total
                if (
                    !empty($offerId = $form->getData()['offer_id']) &&
                    !empty($totalQty = $form->getData()['total_qty']) &&
                    !empty($cartId = $form->getData()['cart_id'])
                ) {
                    $offer = $offerService->findDetailedOfferById($offerId, $this->getUser()->getCompany());
                    $cartService->addOfferToCart(
                        $offer->getOffer(),
                        $this->getUser(),
                        intval($totalQty),
                        $cartId,
                        true
                    );

                    $cart = $cartService->findCart($this->getUser(), $cartId, $offer->getOffer()->getCurrency());

                    if (!$cart) {
                        throw new CartException('Could not find or create cart');
                    }
                    $cartService->resetQtySplitDelivery($cart, $offer->getOffer(), intval($totalQty));
                }

                return new JsonResponse('OK', 200);
            }

            return $this->redirect($request->headers->get('referer'));
        }
        return new Response('Form Error', Response::HTTP_BAD_REQUEST);
    }

    /**
     * add a product to a cart
     * @param Request $request
     * @param $productId
     * @param int|null $cartId
     * @param SpecificPriceService $specificPriceService
     * @param CartService $cartService
     * @param OfferService $offerService
     * @param TranslatorInterface $translator
     * @param ShippingService $shippingService
     * @return Response
     * @throws Exception
     */
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/addFromCart/{productId}', name: 'front.cart.addFromCart')]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/addFromCart/{cartId}/{productId}/', name: 'front.cart.addFromCart.withCartId')]
    public function addOfferFromCartAction(
        Request $request,
        $productId,
        ?int $cartId = null,
        SpecificPriceService $specificPriceService,
        CartService $cartService,
        OfferService $offerService,
        TranslatorInterface $translator,
        ShippingService $shippingService
    )
    {
        $quantity = $request->query->get(self::QUERY_PARAM_QTY);
        $quantity = ($quantity !== null && is_numeric($quantity)) ? intval($quantity) : 1;

        try {
            $offer = $offerService->findDetailedOfferById($productId, $this->getUser()->getCompany());

            if ($offer->getOffer()->getBatchSize() !== null && $quantity % $offer->getOffer()->getBatchSize() > 0) {
                return new Response('', 500);
            }

            $this->removeShippingOptions($cartId, $offer);

            $cartService->addOfferToCart(
                $offer->getOffer(),
                $this->getUser(),
                intval($quantity),
                $cartId,
                true
            );

            $cart = $cartService->findCart($this->getUser(), $cartId, $offer->getOffer()->getCurrency());
            if (!$cart) {
                throw new CartException('Could not find or create cart');
            }
            $cartService->resetQtySplitDelivery($cart, $offer->getOffer(), $quantity);
            foreach ($cart->getMerchants() as $merchantItems) {
                CartItemHelper::setQuantityDemandOrOnStock($merchantItems, "cart");

                foreach ($merchantItems->getItems() as $merchantItem) {
                    if ($merchantItem->getOfferId() == $productId) {
                        $cartService->splitDeliveryCart($merchantItem, $request->getLocale(), $cartId, $this->getUser(), true);
                    }
                }
            }

            if($cartId){
                $addressId = $cartService->findCartAddressId($cartId);

                $shippingService->deleteCache(
                    $this->getUser()->getId(),
                    $cartId,
                    $offer->getOffer()->getMerchant()->getId(),
                    $addressId
                );
            }

            $this->addFlash('success', $translator->trans('cart.add.successUpdate', array(), 'AppBundle'));

        } catch (ApiException $e) {
            $this->addFlash('error', $translator->trans('cart.add.error', array(), 'AppBundle'));
            return new Response('', 500);
        }

        return new Response('', 200);
    }

    /**
     * @param Request $request
     * @param CartService $cartService
     * @param ShippingService $shippingService
     * @param ShippingPointService $shippingPointService
     * @return Response
     * @throws \AppBundle\Exception\MailException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/assign/user/', name: 'cart.details.assign.user', methods: ['POST'])]
    public function assignCartToUserAction(
        Request $request,
        CartService $cartService,
        ShippingService $shippingService,
        ShippingPointService $shippingPointService
    ): Response {
        $user = $this->getUser();
        $resAssign = false;
        $currency = '';

        $siteId = $request->get(self::QUERY_PARAM_SITE_ID);
        $addressId = $request->get(self::QUERY_PARAM_ADDRESS_ID);

        if ($shippingPoint = $shippingPointService->get($request->get('shippingPointId'))) {
            $siteId = $shippingPoint->getSite()->getId();
            $addressId = $shippingPoint->getAddress()->getId();
        }

        try {
            if ($request->isXmlHttpRequest()
                && $request->request->has(self::QUERY_PARAM_CART_ID)
                && $request->request->has(self::QUERY_PARAM_USER_ID)
                && $request->request->has(self::QUERY_PARAM_COMMENT)
                && $siteId
                && $request->request->has(self::QUERY_PARAM_CURRENCY)
                && $addressId
            ) {
                $cartId = intval($request->get(self::QUERY_PARAM_CART_ID));
                $assignUserId = intval($request->get(self::QUERY_PARAM_USER_ID));
                $comment = $request->get(self::QUERY_PARAM_COMMENT);
                $currency = $request->get(self::QUERY_PARAM_CURRENCY);
                $buyerReferenceId = $request->get('buyerReferenceId');
                $additionalForm = $request->get('additionalForm');
                $accountingEmail = $request->get('accountingEmail');
                $additionalFormData = [];

                $convertSerializedFormIntoArray = function (array $serializedForm): array {
                    $formData = [];

                    foreach ($serializedForm as $element) {
                        $name = $element['name'];
                        $value = $element['value'];

                        // define path
                        $path = [];
                        if (preg_match('/[^\[^\]]+/', $name, $root)) {
                            $path[] = $root[0];
                        }

                        if (preg_match_all('/\[([^\[^\]]+)\]+/', $name, $children)) {
                            $path = array_merge($path, $children[1]);
                        }

                        $reversedPath = array_reverse($path);
                        foreach ($reversedPath as $pathStep) {
                            $formElement = [];
                            $formElement[$pathStep] = $value;
                            $value = $formElement;
                        }

                        $formData = array_replace_recursive($formData, $value);
                    }

                    return $formData;
                };

                if ($additionalForm) {
                    $additionalFormData = call_user_func($convertSerializedFormIntoArray, $additionalForm);
                }

                $merchantShippings = $additionalFormData['payment_mode_select_form']['merchantShippings'] ?? [];

                $cart = $cartService->findCart($user, $cartId);
                /* If cart have address saved on shipping cart screen */
                if ($cart->getAddress()) {
                    $shipping = $shippingService->buildCartMerchantShippingOptions($cart, $user);
                    if ($shipping) {
                        $selectedShippings = [];
                        foreach ($merchantShippings as $merchant => $shippingOption) {
                            $merchant = (int)str_replace('merchant-shipping-', '', $merchant);
                            $selectedShippings[$merchant] = $shippingOption;
                        }

                        $shippingService->saveSelectedMerchantShippingOptions($shipping, $selectedShippings);
                    }
                }

                $newBillingAddress = $this->createBillingAddressFromRequest($request, $cart->getBillingAddress());
                $billingAddress = $this->checkIfNewBillingAddress($newBillingAddress);

                $resAssign = $cartService->assignCart(
                    $cartId,
                    $user->getId(),
                    $assignUserId,
                    $siteId,
                    $addressId,
                    $comment,
                    $buyerReferenceId,
                    $accountingEmail,
                    $billingAddress
                );
            }
        } catch (ORMException $e) {
            $resAssign = false;
        }

        if (!$resAssign) {
            return new Response('KO', 500);
        } else {
            // On supprime les éléments du panier de la session utilisateur
            if (strtoupper($currency) === CartService::CURRENCY_USD && $user->getCartUSDId()) {
                $user->setCartUSDId(null);
                $user->setItemInCartUSD(0);
            } else if (strtoupper($currency) === CartService::CURRENCY_EUR && $user->getCartEURId()) {
                $user->setCartEURId(null);
                $user->setItemInCartEUR(0);
            }

            $em = $this->doctrine->getManager();
            $em->persist($user);
            $em->flush();
        }

        return new Response(intval($request->get(self::QUERY_PARAM_CART_ID)), 200);
    }

    /**
     * @param Request $request
     * @param CartService $cartService
     * @param ShippingPointService $shippingPointService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/update/', name: 'cart.details.update')]
    public function updateCartAction(
        Request $request,
        CartService $cartService,
        ShippingPointService $shippingPointService
    ): Response {
        $user = $this->getUser();

        $siteId = intval($request->get(self::QUERY_PARAM_SITE_ID));
        $addressId = intval($request->get(self::QUERY_PARAM_ADDRESS_ID));
        $cartId = intval($request->get(self::QUERY_PARAM_CART_ID));
        $buyerReferenceId = $request->get('buyerReferenceId');
        $selectPaymentMode = $request->get('selectPaymentMode');
        $shippingPointId = intval($request->get(self::QUERY_PARAM_SHIPPING_POINT_ID));
        $documentsRequests = $request->get(self::QUERY_PARAM_DOCUMENTS_REQUESTS, []);
        $accountingEmail = $request->get('accountingEmail');

        $newBillingAddress = $this->createBillingAddressFromRequest($request);

        $billingAddress = $this->checkIfNewBillingAddress($newBillingAddress);

        $result = false;

        try {
            if (
                // $request->isXmlHttpRequest() &&
                $cartId
                && $siteId
                && $addressId
                && $shippingPointService->isValidShippingPointDocumentsRequests($shippingPointId, $documentsRequests)
            ) {
                $cartId = intval($request->get(self::QUERY_PARAM_CART_ID));
                $result = $cartService->updateCart(
                    cartId: $cartId,
                    currentUserId: $user->getId(),
                    siteId: $siteId,
                    addressId: $addressId,
                    buyerReferenceId: $buyerReferenceId,
                    selectPaymentMode: $selectPaymentMode,
                    documentsRequests: $documentsRequests,
                    accountingEmail: $accountingEmail,
                    billingAddress: $billingAddress
                );
            }
        } catch (ORMException $e) {
            return new Response('KO', 500);
        }

        if ($result) {
            return new Response(intval($result), 200);
        } else {
            return new Response('', 500);
        }
    }

    /**
     * @param Request $request
     * @param CartService $cartService
     * @return Response
     * @throws \AppBundle\Exception\MailException
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/reject/', name: 'cart.details.reject', methods: ['POST'])]
    public function rejectCartAssignAction(
        Request $request,
        CartService $cartService
    )
    {
        $resReject = true;

        try {
            if ($request->isXmlHttpRequest() && $request->request->has(self::QUERY_PARAM_CART_ID) && $request->request->has(self::QUERY_PARAM_COMMENT)) {
                $cartId = intval($request->request->get(self::QUERY_PARAM_CART_ID));
                $comment = $request->request->get(self::QUERY_PARAM_COMMENT);
                $resReject = $cartService->rejectAssignCart($cartId, $comment, $this->getUser());
            }
        } catch (ORMException $e) {
            $resReject = false;
        }

        if (!$resReject) {
            return new Response('KO', 500);
        }

        return new Response(intval($request->request->get(self::QUERY_PARAM_CART_ID)), 200);
    }

    /**
     * @param CartService $cartService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/pending/list', name: 'cart.pending.list')]
    public function getPendingCartByCompanyIdAction(
        CartService $cartService
    )
    {
        $carts = $cartService->getPendingCartByUser($this->getUser());

        return $this->render('@OpenFront/cart/cart_pending_list.html.twig', [
            'carts' => $carts,
            'user' => $this->getUser(),
            'company' => $this->getCompany()
        ]);
    }

    /**
     * @param Request $request
     * @param int $cartId
     * @param CartService $cartService
     * @param CartFactory $cartFactory
     * @param WishListService $wishListService
     * @param SiteService $siteService
     * @param SecurityService $securityService
     * @param CartNotificationService $cartNotificationService
     * @param PaymentService $paymentService
     * @param WPSService $wpsService
     * @param OrderService $orderService
     * @param MerchantOrderService $merchantOrderService
     * @param TranslatorInterface $translator
     * @param JobService $jobService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/pending/{cartId}', name: 'cart.pending.details')]
    public function cartPendingDetailsAction(
        Request $request,
        int $cartId,
        CartService $cartService,
        CartFactory $cartFactory,
        WishListService $wishListService,
        SiteService $siteService,
        SecurityService $securityService,
        CartNotificationService $cartNotificationService,
        PaymentService $paymentService,
        WPSService $wpsService,
        OrderService $orderService,
        MerchantOrderService $merchantOrderService,
        TranslatorInterface $translator,
        JobService $jobService
    ): Response {
        /** @var User $user */
        $user = $this->getUser();
        $cart = $cartService->findCart($user, $cartId);
        $cart = $cartFactory->decorateWithShipping($cart);

        // All without api and administrator can split delivery date
        $cart->canSplitDeliveryDate = $user->canSplitDeliveryDate();

        // set quantity demand or on stock
        foreach ($cart->getMerchants() as $merchant) {
            CartItemHelper::setQuantityDemandOrOnStock($merchant, "cart");

            foreach ($merchant->getItems() as $merchantItem) {
                $cartService->splitDeliveryCart($merchantItem, $request->getLocale(), $cartId, $user);
            }
        }

        $isCartCreator = $cartService->isUserCartCreator($user, $cart->getId());

        if ($isCartCreator) {
            return $this->redirectToRoute('cart.details.before_buy', ['currencyOrCartId' => $cartId]);
        }

        $isUserAuthorizeToBuy = $cartService->isUserAuthorizedToBuy($user);
        $isUserValidAssign = $cartService->isUserValidAssign($user, $cart->getId());
        $isUserAuthorizeReject = $cartService->isUserAuthorizeReject($user, $cart->getId());
        $isUserAuthorizeToEditCart = $isUserValidAssign;

        $sites = [];
        // Find users for assign cart if not exists
        if (!is_null($user->getCompany())) {
            //if user is buyer admin, he can all sites
            if ($securityService->isAdminCompany($user)) {
                $sites = $siteService->getSitesWithAtLeastOneAddressByCompany($user->getCompany()->getId());
            } else {
                $sites = $siteService->getUserSitesWithAtLeastOneAddress($user);
            }
        }

        //in addition, we need to add stuff relative to payment selection
        $paymentModesFomElement = $this->initPaymentModesForm($request, $cart, $paymentService);

        /** @var Form $form */
        $form = $paymentModesFomElement['form']->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $cartNotificationService->checkCartNotification($cart);
            if (!$cart->hasNotifications()) {
                $cartService->checkoutCart(
                    $cart,
                    $user,
                    $form->getData()['addressId'] ?? $cart->getShippingPoint()->getId(),
                    $form->getData()['term'],
                    $request->getLocale(),
                    $cart->getBillingAddress()
                );

                return $this->handlePaymentRequest(
                    cart: $cart,
                    cartService: $cartService,
                    wpsService: $wpsService,
                    orderService: $orderService,
                    merchantOrderService: $merchantOrderService,
                    translator: $translator,
                    jobService: $jobService,
                    validationNumber: $form->getData()['validationNumber'],
                    accountingEmail: $form->getData()['accountingEmail']
                );
            }

            /** @var CartNotification $cartNotification */
            foreach ($cart->getNotifications() as $cartNotification) {
                $cartNotificationService->applyCartNotification($cartNotification);
                $this->addFlash(
                    'info',
                    $cartNotification->getMessage()
                );
            }

            // return $this->redirectToRoute('cart.pending.details', ['cartId' => $cartId]);
        }

        return $this->render(
            '@OpenFront/cart/cart_pending_details.html.twig',
            array_merge(
                $paymentModesFomElement['viewParams'],
                [
                    'user' => $this->getUser(),
                    'cart' => $cart,
                    'currencyOrCartId' => $cartId,
                    'isUserAuthorizeToBuy' => $isUserAuthorizeToBuy,  //whether the user has BUYER_PAYER or BUYER_ADMIN role
                    'isUserValidAssign' => $isUserValidAssign,        //whether the current user is the assigned cart user
                    'isUserAuthorizeToEditCart' => $isUserAuthorizeToEditCart, //whether the current user is authorized to edit this cart
                    'isUserAuthorizeToAutoAssign' => !$isUserAuthorizeToEditCart && $cartService->isUserInAssignmentChains($this->getUser(), $cart->getId()),
                    'isUserAuthorizeReject' => $isUserAuthorizeReject,
                    'sites' => $sites,
                    'isCartCreator' => $isCartCreator,
                    'historic' => array_filter(
                        $cartService->getCartHistoric($cart->getId()),
                        function (CartHistoric $cartHistoric) {
                            return (
                                $cartHistoric->getStatus() === Cart::STATUS_ASSIGN
                                || $cartHistoric->getStatus() === Cart::STATUS_CREATE
                                || $cartHistoric->getStatus() === Cart::STATUS_REJECTED
                            );
                        }
                    ),
                    'companyValid' => $this->validateCompany(),
                    'wishLists' => $wishListService->getUserWishList($user->getId(), $cart->getCurrency()),
                    'shipping' => []
                ]
            )
        );
    }

    /**
     * @param Request $request
     * @param AddressService $addressService
     * @return Response
     */
    #[IsGranted(new Expression('is_granted("ROLE_BUYER_ADMIN") or is_granted("ROLE_BUYER_PAYER") or is_granted("ROLE_BUYER_BUYER")'))]
    #[\Symfony\Component\Routing\Attribute\Route(path: '/cart/shipping-address', name: 'cart.shipping.address')]
    public function selectShippingAddressAction(
        Request $request,
        AddressService $addressService
    )
    {
        $cartId = $request->get('cartId');
        $shippingAddressId = $request->get('shippingAddressId');
        $shippingAddress = $addressService->get($shippingAddressId);
        $em = $this->doctrine->getManager();

        /** @var Cart $cart */
        $cart = $em->getRepository(Cart::class)->find($cartId);

        $cart->setAddress($shippingAddress);

        $em->persist($cart);
        $em->flush();

        return new Response();

    }

    private function removeShippingOptions(?int $cartId, ?DetailedOffer $offer = null)
    {
        $em = $this->doctrine->getManager();

        $filters['cart'] = $cartId;
        if (!empty($offer)) $filters['merchantId'] = $offer->getOffer()->getMerchant()->getId();

        $shippingOptions = $em->getRepository(CartShippingOption::class)->findBy($filters);

        if (!empty($shippingOptions)) {
            foreach ($shippingOptions as $shippingOption) {
                $em->remove($shippingOption);
            }
            $em->flush();
        }
    }

    private function checkIfNewBillingAddress(Address $billingAddress): ?Address {
        $company = $this->getCompany();
        $billingAddress->setCountry($company->getMainAddress()->getCountry());
        if ($company->getBillingOrMainAddress()->isEqual($billingAddress)) {
            $billingAddress = $company->getBillingOrMainAddress();
        }

        return $billingAddress;
    }

    private function createBillingAddressFromRequest(Request $request, ?Address $billingAddress = null): ?Address {
        $billingAddressMain = $request->get('billingAddressMain');
        $billingAddressComplement = $request->get('billingAddressComplement');
        $billingAddressZipCode = $request->get('billingAddressZipCode');
        $billingAddressCity = $request->get('billingAddressCity');
        $billingAddressArea = $request->get('billingAddressArea');

        if ($billingAddressComplement === '') {
            $billingAddressComplement = null;
        }

        if ($billingAddress === null || $this->getCompany()->getBillingOrMainAddress()->isEqual($billingAddress)) {
            $billingAddress = new Address();
        }

        $billingAddress->setAddress($billingAddressMain);
        $billingAddress->setAddress2($billingAddressComplement);
        $billingAddress->setZipCode($billingAddressZipCode);
        $billingAddress->setCity($billingAddressCity);
        $billingAddress->setRegionText($billingAddressArea);

        return $billingAddress;
    }
}
